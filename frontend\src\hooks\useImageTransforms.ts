import { useState } from "react";
import { Canvas } from "fabric";
import { TransformState, ImageTransformsState } from "@/shared/types";
import {
  createRotateHandler,
  createFlipHorizontalHandler,
  createFlipVerticalHandler,
} from "@/lib/fabric/operations";
import {
  applyCanvasRotation,
  applyCanvasFlipHorizontal,
  applyCanvasFlipVertical,
} from "@/lib/fabric/operations/transforms";

export const useImageTransforms = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialTransformState: TransformState = {
    rotations: 0,
    flipHorizontal: false,
    flipVertical: false,
  },
  onRotationComplete?: () => void
): ImageTransformsState => {
  const [transformState, setTransformState] = useState<TransformState>(initialTransformState);

  const handleRotate = createRotateHandler(fabricCanvas, setTransformState, onRotationComplete);
  const handleFlipHorizontal = createFlipHorizontalHandler(fabricCanvas, setTransformState);
  const handleFlipVertical = createFlipVerticalHandler(fabricCanvas, setTransformState);

  const applySavedTransforms = () => {
    if (!fabricCanvas.current) return;

    const canvas = fabricCanvas.current;

    for (let i = 0; i < transformState.rotations; i++) {
      applyCanvasRotation(canvas, onRotationComplete);
    }

    if (transformState.flipHorizontal) {
      applyCanvasFlipHorizontal(canvas);
    }

    if (transformState.flipVertical) {
      applyCanvasFlipVertical(canvas);
    }
  };

  return {
    transformState,
    setTransformState,
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
    applySavedTransforms,
  };
};
