import React, { useState } from "react";
import { ImageToolbarProps } from "@/shared/types";
import { useFabricTools } from "@/hooks/useFabricTools";
import {
  ToolGrid,
  TransformControls,
  SliderControls,
  GammaControls,
  ActionButtons,
} from "./components";

import { Rnd } from "react-rnd";

const FabricToolbar: React.FC<ImageToolbarProps> = ({
  fabricCanvas,
  brightness,
  contrast,
  grayscale,
  invert,
  sharpness,
  gammaR,
  gammaG,
  gammaB,
  onBrightnessChange,
  onContrastChange,
  onGrayscaleChange,
  onInvertChange,
  onSharpnessChange,
  onGammaRChange,
  onGammaGChange,
  onGammaBChange,
  onRotate,
  onFlipHorizontal,
  onFlipVertical,
  onUndo,
  canUndo = false,
  onSave,
  onShowOriginal,
  onShapeCreated,
  onCrop,
  disableGrayscale = false,
  disableGamma = false,
  disableUndoTracking,
  enableUndoTracking,
  isShowingOriginal = false,
  hasPerformedCrop = false,
}) => {
  const { activeMode, changeToolMode } = useFabricTools({
    fabricCanvas,
    isShowingOriginal,
    hasPerformedCrop,
    onShapeCreated,
    onCrop,
    disableUndoTracking,
    enableUndoTracking,
  });
  const [position, setPosition] = useState({ x: 0, y: window.innerHeight / 2 });

  return (
    <Rnd
      position={position}
      onDragStop={(_e, d) => {
        setPosition({ x: d.x, y: d.y });
      }}
    >
      <div className={`fabric-toolbar-vertical ${isShowingOriginal ? "disabled" : ""}`}>
        <div className="annotation-tools">
          <ToolGrid
            activeMode={activeMode}
            isShowingOriginal={isShowingOriginal}
            hasPerformedCrop={hasPerformedCrop}
            onToolSelect={changeToolMode}
            onCrop={onCrop}
          />

          <TransformControls
            isShowingOriginal={isShowingOriginal}
            grayscale={grayscale}
            invert={invert}
            disableGrayscale={disableGrayscale}
            hasPerformedCrop={hasPerformedCrop}
            onRotate={onRotate}
            onFlipHorizontal={onFlipHorizontal}
            onFlipVertical={onFlipVertical}
            onGrayscaleChange={onGrayscaleChange}
            onInvertChange={onInvertChange}
            onShowOriginal={onShowOriginal}
          />
        </div>

        <SliderControls
          brightness={brightness}
          contrast={contrast}
          sharpness={sharpness}
          isShowingOriginal={isShowingOriginal}
          onBrightnessChange={onBrightnessChange}
          onContrastChange={onContrastChange}
          onSharpnessChange={onSharpnessChange}
        />

        <GammaControls
          gammaR={gammaR}
          gammaG={gammaG}
          gammaB={gammaB}
          disableGamma={disableGamma}
          isShowingOriginal={isShowingOriginal}
          onGammaRChange={onGammaRChange}
          onGammaGChange={onGammaGChange}
          onGammaBChange={onGammaBChange}
        />

        <ActionButtons
          canUndo={canUndo}
          isShowingOriginal={isShowingOriginal}
          onUndo={onUndo}
          onSave={onSave}
        />
      </div>
    </Rnd>
  );
};

export default FabricToolbar;
