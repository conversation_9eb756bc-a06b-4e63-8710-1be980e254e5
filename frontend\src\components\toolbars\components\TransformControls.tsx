import React from "react";
import {
  FaRedo,
  FaArrowsAltH,
  FaArrowsAltV,
  FaPalette,
  FaExchangeAlt,
  FaImage,
} from "react-icons/fa";
import type { TransformControlsProps } from "@/shared/types";

const TransformControls: React.FC<TransformControlsProps> = ({
  isShowingOriginal,
  grayscale,
  invert,
  disableGrayscale,
  hasPerformedCrop,
  onRotate,
  onFlipHorizontal,
  onFlipVertical,
  onGrayscaleChange,
  onInvertChange,
  onShowOriginal,
}) => {
  return (
    <>
      <div className="tool-grid">
        <button
          className={`tool-btn ${isShowingOriginal || hasPerformedCrop ? "disabled" : ""}`}
          onClick={isShowingOriginal || hasPerformedCrop ? undefined : onRotate}
          disabled={isShowingOriginal || hasPerformedCrop}
          title={hasPerformedCrop ? "Rotate disabled for cropped images" : "Rotate selected object"}
        >
          <FaRedo />
        </button>
        <button
          className={`tool-btn ${isShowingOriginal || hasPerformedCrop ? "disabled" : ""}`}
          onClick={isShowingOriginal || hasPerformedCrop ? undefined : onFlipHorizontal}
          disabled={isShowingOriginal || hasPerformedCrop}
          title={hasPerformedCrop ? "Flip disabled for cropped images" : "Flip horizontal"}
        >
          <FaArrowsAltH />
        </button>
        <button
          className={`tool-btn ${isShowingOriginal || hasPerformedCrop ? "disabled" : ""}`}
          onClick={isShowingOriginal || hasPerformedCrop ? undefined : onFlipVertical}
          disabled={isShowingOriginal || hasPerformedCrop}
          title={hasPerformedCrop ? "Flip disabled for cropped images" : "Flip vertical"}
        >
          <FaArrowsAltV />
        </button>
        <button
          className={`tool-btn ${grayscale ? "active" : ""} ${
            disableGrayscale || isShowingOriginal ? "disabled" : ""
          }`}
          onClick={() => !disableGrayscale && !isShowingOriginal && onGrayscaleChange(!grayscale)}
          disabled={disableGrayscale || isShowingOriginal}
          title="Toggle grayscale"
        >
          <FaPalette />
        </button>
        <button
          className={`tool-btn ${invert ? "active" : ""} ${isShowingOriginal ? "disabled" : ""}`}
          onClick={() => !isShowingOriginal && onInvertChange(!invert)}
          disabled={isShowingOriginal}
          title="Toggle invert"
        >
          <FaExchangeAlt />
        </button>
        {onShowOriginal && (
          <button
            className={`tool-btn ${isShowingOriginal ? "active" : ""}`}
            onClick={onShowOriginal}
            title={isShowingOriginal ? "Exit show original mode" : "Show original image"}
          >
            <FaImage />
          </button>
        )}
      </div>
    </>
  );
};

export default TransformControls;
